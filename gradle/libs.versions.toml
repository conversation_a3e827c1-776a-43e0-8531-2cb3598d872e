[versions]
# libraries
junit = "4.13.2"
opentest4j = "1.3.0"
jewel = "1.0.0-SNAPSHOT"
coroutines = "1.8.0"
okhttp = "4.12.0"
gson = "2.10.1"

# plugins
changelog = "2.2.1"
intelliJPlatform = "2.6.0"
kotlin = "2.1.20"
kover = "0.9.1"
qodana = "2025.1.1"
qodana = "2024.3.4"
composeDesktop = "1.7.0"

serialization-json = "1.8.0"
okhttp = "4.12.0"
composeTesting = "1.0.0"

[libraries]
junit = { group = "junit", name = "junit", version.ref = "junit" }
opentest4j = { group = "org.opentest4j", name = "opentest4j", version.ref = "opentest4j" }
okhttp = { group = "com.squareup.okhttp3", name = "okhttp", version.ref = "okhttp" }
gson = { group = "com.google.code.gson", name = "gson", version.ref = "gson" }

kotlinx-coroutines-core = { module = "org.jetbrains.kotlinx:kotlinx-coroutines-core", version.ref = "coroutines" }
kotlinx-coroutines-test = { module = "org.jetbrains.kotlinx:kotlinx-coroutines-test", version.ref = "coroutines" }
jewel-bridge-ij241 = { module = "org.jetbrains.jewel:jewel-ide-laf-bridge-241", version.ref = "jewel" }
jewel-bridge-ij242 = { module = "org.jetbrains.jewel:jewel-ide-laf-bridge-242", version.ref = "jewel" }
jewel-bridge-ij243 = { module = "org.jetbrains.jewel:jewel-ide-laf-bridge-243", version.ref = "jewel" }
jewel-markdown-ij241 = { module ="org.jetbrains.jewel:jewel-markdown-core-241", version.ref = "jewel" }
jewel-markdown-ij242 = { module ="org.jetbrains.jewel:jewel-markdown-core-242", version.ref = "jewel" }
jewel-markdown-ij243 = { module ="org.jetbrains.jewel:jewel-markdown-core-243", version.ref = "jewel" }
jewel-ui-ij241 = { module = "org.jetbrains.jewel:jewel-ui-241", version.ref = "jewel" }
jewel-ui-ij242 = { module = "org.jetbrains.jewel:jewel-ui-242", version.ref = "jewel" }
jewel-ui-ij243 = { module = "org.jetbrains.jewel:jewel-ui-243", version.ref = "jewel" }
jewel-markdown-laf-bridge-ij241 = { module = "org.jetbrains.jewel:jewel-markdown-ide-laf-bridge-styling-241", version.ref="jewel" }
jewel-markdown-laf-bridge-ij242 = { module = "org.jetbrains.jewel:jewel-markdown-ide-laf-bridge-styling-242", version.ref="jewel" }
jewel-markdown-laf-bridge-ij243 = { module = "org.jetbrains.jewel:jewel-markdown-ide-laf-bridge-styling-243", version.ref="jewel" }
compose-desktop-components-splitpane = { module = "org.jetbrains.compose.components:components-splitpane", version.ref = "composeDesktop" }
kotlinx-serialization-json = { module = "org.jetbrains.kotlinx:kotlinx-serialization-json", version.ref = "serialization-json" }
okhttp = { module = "com.squareup.okhttp3:okhttp", version.ref = "okhttp" }
okhttp-sse = { module = "com.squareup.okhttp3:okhttp-sse", version.ref = "okhttp" }
okhttp-mockserver = { module = "com.squareup.okhttp3:mockwebserver", version.ref = "okhttp" }
compose-testing = { module = "androidx.compose.ui:ui-test-junit4", version.ref = "composeTesting" }

[plugins]
changelog = { id = "org.jetbrains.changelog", version.ref = "changelog" }
intelliJPlatform = { id = "org.jetbrains.intellij.platform", version.ref = "intelliJPlatform" }
kotlin = { id = "org.jetbrains.kotlin.jvm", version.ref = "kotlin" }
kover = { id = "org.jetbrains.kotlinx.kover", version.ref = "kover" }
qodana = { id = "org.jetbrains.qodana", version.ref = "qodana" }


kotlin-plugin-compose = { id = "org.jetbrains.kotlin.plugin.compose", version.ref = "kotlin" }
compose-desktop = { id = "org.jetbrains.compose", version.ref = "composeDesktop" }
serialization = { id = "org.jetbrains.kotlin.plugin.serialization", version.ref = "serialization-json" }
shadow = { id = "com.gradleup.shadow", version = "8.3.3" }
