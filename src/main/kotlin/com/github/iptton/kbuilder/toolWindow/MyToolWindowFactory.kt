package com.github.iptton.kbuilder.toolWindow

import com.intellij.openapi.components.service
import com.intellij.openapi.diagnostic.thisLogger
import com.intellij.openapi.project.Project
import com.intellij.openapi.wm.ToolWindow
import com.intellij.openapi.wm.ToolWindowFactory
import com.intellij.ui.components.JBLabel
import com.intellij.ui.components.JBPanel
import com.intellij.ui.components.JBScrollPane
import com.intellij.ui.components.JBTextArea
import com.intellij.ui.content.ContentFactory
import com.github.iptton.kbuilder.MyBundle
import com.github.iptton.kbuilder.services.MyProjectService
import com.github.iptton.kbuilder.auth.GitHubDeviceFlowAuth
import com.github.iptton.kbuilder.auth.AuthState
import com.github.iptton.kbuilder.auth.AuthResult
import com.github.iptton.kbuilder.copilot.CopilotApiService
import com.github.iptton.kbuilder.copilot.CopilotApiResult
import com.github.iptton.kbuilder.copilot.CopilotRequestBuilder
import kotlinx.coroutines.*
import java.awt.BorderLayout
import java.awt.Desktop
import java.awt.Dimension
import java.awt.GridBagConstraints
import java.awt.GridBagLayout
import java.awt.Insets
import java.net.URI
import javax.swing.*


class MyToolWindowFactory : ToolWindowFactory {

    init {
        thisLogger().warn("GitHub Copilot Plugin Tool Window")
    }

    override fun createToolWindowContent(project: Project, toolWindow: ToolWindow) {
        val myToolWindow = MyToolWindow(toolWindow)
        val content = ContentFactory.getInstance().createContent(myToolWindow.getContent(), "GitHub Copilot", false)
        toolWindow.contentManager.addContent(content)
    }

    override fun shouldBeAvailable(project: Project) = true

    class MyToolWindow(toolWindow: ToolWindow) {

        private val logger = thisLogger()
        private val authService = service<GitHubDeviceFlowAuth>()
        private val copilotService = service<CopilotApiService>()
        private val coroutineScope = CoroutineScope(Dispatchers.Main + SupervisorJob())

        // UI Components
        private val statusLabel = JBLabel("Not authenticated")
        private val authButton = JButton("Authenticate with GitHub")
        private val signOutButton = JButton("Sign Out")
        private val testButton = JButton("Test Copilot API")
        private val getModelsButton = JButton("Get Available Models")

        private val promptArea = JBTextArea(3, 40)
        private val responseArea = JBTextArea(15, 40)
        private val sendButton = JButton("Send to Copilot")

        init {
            setupUI()
            updateAuthState()
        }

        fun getContent(): JComponent {
            return createMainPanel()
        }

        private fun createMainPanel(): JPanel {
            val mainPanel = JBPanel<JBPanel<*>>(BorderLayout())

            // Authentication Panel
            val authPanel = createAuthPanel()
            mainPanel.add(authPanel, BorderLayout.NORTH)

            // Chat Panel
            val chatPanel = createChatPanel()
            mainPanel.add(chatPanel, BorderLayout.CENTER)

            return mainPanel
        }

        private fun createAuthPanel(): JPanel {
            val panel = JBPanel<JBPanel<*>>(GridBagLayout())
            val gbc = GridBagConstraints()

            gbc.insets = Insets(5, 5, 5, 5)
            gbc.anchor = GridBagConstraints.WEST

            // Status
            gbc.gridx = 0; gbc.gridy = 0
            panel.add(JBLabel("Status:"), gbc)

            gbc.gridx = 1; gbc.gridy = 0
            gbc.weightx = 1.0
            gbc.fill = GridBagConstraints.HORIZONTAL
            panel.add(statusLabel, gbc)

            // Buttons
            gbc.gridx = 0; gbc.gridy = 1
            gbc.weightx = 0.0
            gbc.fill = GridBagConstraints.NONE
            panel.add(authButton, gbc)

            gbc.gridx = 1; gbc.gridy = 1
            panel.add(signOutButton, gbc)

            gbc.gridx = 2; gbc.gridy = 1
            panel.add(testButton, gbc)

            gbc.gridx = 3; gbc.gridy = 1
            panel.add(getModelsButton, gbc)

            return panel
        }

        private fun createChatPanel(): JPanel {
            val panel = JBPanel<JBPanel<*>>(BorderLayout())
            panel.border = BorderFactory.createTitledBorder("Chat with Copilot")

            // Prompt area
            val promptPanel = JBPanel<JBPanel<*>>(BorderLayout())
            promptPanel.add(JBLabel("Your message:"), BorderLayout.NORTH)

            promptArea.lineWrap = true
            promptArea.wrapStyleWord = true
            promptArea.text = "Hello! Can you help me write a simple Kotlin function?"

            val promptScroll = JBScrollPane(promptArea)
            promptScroll.preferredSize = Dimension(400, 80)
            promptPanel.add(promptScroll, BorderLayout.CENTER)
            promptPanel.add(sendButton, BorderLayout.SOUTH)

            panel.add(promptPanel, BorderLayout.NORTH)

            // Response area
            val responsePanel = JBPanel<JBPanel<*>>(BorderLayout())
            responsePanel.add(JBLabel("Copilot response:"), BorderLayout.NORTH)

            responseArea.isEditable = false
            responseArea.lineWrap = true
            responseArea.wrapStyleWord = true
            responseArea.text = "Responses will appear here..."

            val responseScroll = JBScrollPane(responseArea)
            responseScroll.preferredSize = Dimension(400, 300)
            responsePanel.add(responseScroll, BorderLayout.CENTER)

            panel.add(responsePanel, BorderLayout.CENTER)

            return panel
        }

        private fun setupUI() {
            // Authentication button
            authButton.addActionListener {
                authenticateWithGitHub()
            }

            // Sign out button
            signOutButton.addActionListener {
                signOut()
            }

            // Test button
            testButton.addActionListener {
                testCopilotConnection()
            }

            // Get models button
            getModelsButton.addActionListener {
                getAvailableModels()
            }

            // Send button
            sendButton.addActionListener {
                sendMessageToCopilot()
            }

            // Enter key in prompt area
            promptArea.addKeyListener(object : java.awt.event.KeyAdapter() {
                override fun keyPressed(e: java.awt.event.KeyEvent) {
                    if (e.keyCode == java.awt.event.KeyEvent.VK_ENTER && e.isControlDown) {
                        sendMessageToCopilot()
                        e.consume()
                    }
                }
            })
        }

        private fun updateAuthState() {

            coroutineScope.launch {
                val authState = authService.getAuthState()

                when (authState) {
                    AuthState.NOT_AUTHENTICATED -> {
                        statusLabel.text = "Not authenticated"
                        authButton.isEnabled = true
                        signOutButton.isEnabled = false
                        testButton.isEnabled = false
                        getModelsButton.isEnabled = false
                        sendButton.isEnabled = false
                    }

                    AuthState.DEVICE_FLOW_PENDING -> {
                        statusLabel.text = "Authentication in progress..."
                        authButton.isEnabled = false
                        signOutButton.isEnabled = true
                        testButton.isEnabled = false
                        getModelsButton.isEnabled = false
                        sendButton.isEnabled = false
                    }

                    AuthState.AUTHENTICATED -> {
                        statusLabel.text = "Authenticated ✓"
                        authButton.isEnabled = false
                        signOutButton.isEnabled = true
                        testButton.isEnabled = true
                        getModelsButton.isEnabled = true
                        sendButton.isEnabled = true
                    }

                    AuthState.TOKEN_EXPIRED -> {
                        statusLabel.text = "Token expired - please re-authenticate"
                        authButton.isEnabled = true
                        signOutButton.isEnabled = true
                        testButton.isEnabled = false
                        getModelsButton.isEnabled = false
                        sendButton.isEnabled = false
                    }

                    AuthState.ERROR -> {
                        statusLabel.text = "Authentication error"
                        authButton.isEnabled = true
                        signOutButton.isEnabled = true
                        testButton.isEnabled = false
                        getModelsButton.isEnabled = false
                        sendButton.isEnabled = false
                    }
                }
            }
        }

        private fun authenticateWithGitHub() {
            coroutineScope.launch {
                try {
                    responseArea.text = "Starting GitHub authentication...\n"

                    val deviceFlowResult = authService.startDeviceFlow()
                    when (deviceFlowResult) {
                        is AuthResult.Success -> {
                            val deviceFlow = deviceFlowResult.data
                            responseArea.text += "Please visit: ${deviceFlow.verification_uri}\n"
                            responseArea.text += "And enter code: ${deviceFlow.user_code}\n\n"

                            // Try to open browser
                            try {
                                if (Desktop.isDesktopSupported()) {
                                    Desktop.getDesktop().browse(URI(deviceFlow.verification_uri))
                                    responseArea.text += "Browser opened automatically.\n"
                                }
                            } catch (e: Exception) {
                                logger.warn("Could not open browser", e)
                                responseArea.text += "Please open the URL manually.\n"
                            }

                            updateAuthState()

                            // Start polling
                            authService.startPolling(
                                deviceFlow,
                                onSuccess = { tokenResponse ->
                                    SwingUtilities.invokeLater {
                                        responseArea.text += "Authentication successful! ✓\n"
                                        updateAuthState()
                                    }
                                },
                                onError = { error ->
                                    SwingUtilities.invokeLater {
                                        responseArea.text += "Authentication failed: $error\n"
                                        updateAuthState()
                                    }
                                },
                                onPending = { message ->
                                    SwingUtilities.invokeLater {
                                        statusLabel.text = message
                                    }
                                }
                            )
                        }
                        is AuthResult.Error -> {
                            responseArea.text += "Failed to start authentication: ${deviceFlowResult.message}\n"
                            updateAuthState()
                        }
                        is AuthResult.Pending -> {
                            responseArea.text += "Authentication pending: ${deviceFlowResult.message}\n"
                        }
                    }
                } catch (e: Exception) {
                    logger.error("Error during authentication", e)
                    responseArea.text += "Error: ${e.message}\n"
                    updateAuthState()
                }
            }
        }

        private fun signOut() {
            authService.signOut()
            responseArea.text = "Signed out successfully.\n"
            updateAuthState()
        }

        private fun testCopilotConnection() {
            coroutineScope.launch {
                try {
                    responseArea.text = "Testing Copilot connection...\n"

                    val result = copilotService.simpleChat("Hello! Please respond with a simple greeting.")
                    when (result) {
                        is CopilotApiResult.Success -> {
                            responseArea.text += "✓ Copilot connection successful!\n"
                            responseArea.text += "Response: ${result.data}\n\n"
                        }
                        is CopilotApiResult.Error -> {
                            responseArea.text += "✗ Copilot connection failed: ${result.message}\n\n"
                        }
                    }
                } catch (e: Exception) {
                    logger.error("Error testing Copilot connection", e)
                    responseArea.text += "Error: ${e.message}\n\n"
                }
            }
        }

        private fun getAvailableModels() {
            coroutineScope.launch {
                try {
                    responseArea.text = "Fetching available models...\n"

                    val result = copilotService.getModels()
                    when (result) {
                        is CopilotApiResult.Success -> {
                            responseArea.text += "Available models:\n"
                            result.data.data.forEach { model ->
                                responseArea.text += "- ${model.id} (${model.owned_by ?: "unknown"})\n"
                            }
                            responseArea.text += "\n"
                        }
                        is CopilotApiResult.Error -> {
                            responseArea.text += "Failed to get models: ${result.message}\n\n"
                        }
                    }
                } catch (e: Exception) {
                    logger.error("Error getting models", e)
                    responseArea.text += "Error: ${e.message}\n\n"
                }
            }
        }

        private fun sendMessageToCopilot() {
            val message = promptArea.text.trim()
            if (message.isEmpty()) {
                return
            }

            coroutineScope.launch {
                try {
                    responseArea.text += "You: $message\n\n"
                    responseArea.text += "Copilot is thinking...\n"

                    val result = copilotService.simpleChat(
                        message = message,
                        systemPrompt = "You are a helpful coding assistant. Provide clear, concise, and accurate responses."
                    )

                    when (result) {
                        is CopilotApiResult.Success -> {
                            responseArea.text = responseArea.text.replace("Copilot is thinking...\n", "")
                            responseArea.text += "Copilot: ${result.data}\n\n"
                            responseArea.text += "---\n\n"
                        }
                        is CopilotApiResult.Error -> {
                            responseArea.text = responseArea.text.replace("Copilot is thinking...\n", "")
                            responseArea.text += "Error: ${result.message}\n\n"
                        }
                    }

                    // Scroll to bottom
                    SwingUtilities.invokeLater {
                        responseArea.caretPosition = responseArea.document.length
                    }

                } catch (e: Exception) {
                    logger.error("Error sending message to Copilot", e)
                    responseArea.text += "Error: ${e.message}\n\n"
                }
            }
        }
    }
}
