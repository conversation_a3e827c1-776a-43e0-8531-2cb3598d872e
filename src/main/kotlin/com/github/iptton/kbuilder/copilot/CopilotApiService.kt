package com.github.iptton.kbuilder.copilot

import com.intellij.openapi.components.Service
import com.intellij.openapi.components.service
import com.intellij.openapi.diagnostic.thisLogger
import com.github.iptton.kbuilder.auth.AuthResult
import com.google.gson.Gson
import okhttp3.*
import okhttp3.MediaType.Companion.toMediaType
import okhttp3.RequestBody.Companion.toRequestBody
import java.io.IOException
import java.util.concurrent.TimeUnit
import kotlinx.coroutines.*

/**
 * GitHub Copilot API service for chat and text completions
 */
@Service
class CopilotApiService {

    private val tokenManager = service<CopilotTokenManager>()
    
    private val logger = thisLogger()
    private val gson = Gson()
    private val httpClient = OkHttpClient.Builder()
        .connectTimeout(60, TimeUnit.SECONDS)
        .readTimeout(60, TimeUnit.SECONDS)
        .writeTimeout(60, TimeUnit.SECONDS)
        .build()
    
    companion object {
        private const val COPILOT_BASE_URL = "https://api.githubcopilot.com"
        private const val MODELS_ENDPOINT = "$COPILOT_BASE_URL/models"
        private const val CHAT_COMPLETIONS_ENDPOINT = "$COPILOT_BASE_URL/chat/completions"
        private const val TEXT_COMPLETIONS_ENDPOINT = "$COPILOT_BASE_URL/completions"
        
        private const val USER_AGENT = "GitHub-Copilot-IntelliJ-Plugin/1.0"
        private const val EDITOR_VERSION = "IntelliJ/2024.2"
        private const val COPILOT_INTEGRATION_ID = "vscode-chat"
    }
    
    /**
     * Get available models from Copilot API
     */
    suspend fun getModels(): CopilotApiResult<ModelsResponse> = withContext(Dispatchers.IO) {
        try {
            val tokenResult = tokenManager.getValidCopilotToken()
            if (tokenResult !is AuthResult.Success<*>) {
                return@withContext CopilotApiResult.Error("Failed to get Copilot token: ${(tokenResult as AuthResult.Error).message}")
            }
            
            val request = Request.Builder()
                .url(MODELS_ENDPOINT)
                .get()
                .header("Authorization", "Bearer ${tokenResult.data}")
                .header("Editor-Version", EDITOR_VERSION)
                .header("Content-Type", "application/json")
                .header("Copilot-Integration-Id", COPILOT_INTEGRATION_ID)
                .header("User-Agent", USER_AGENT)
                .build()
            
            val response = httpClient.newCall(request).execute()
            
            if (response.isSuccessful) {
                val responseBody = response.body?.string()
                if (responseBody != null) {
                    val modelsResponse = gson.fromJson(responseBody, ModelsResponse::class.java)
                    logger.info("Successfully retrieved ${modelsResponse.data.size} models")
                    CopilotApiResult.Success(modelsResponse)
                } else {
                    CopilotApiResult.Error("Empty response from models endpoint")
                }
            } else {
                handleErrorResponse(response, "Failed to get models")
            }
        } catch (e: IOException) {
            logger.error("Network error getting models", e)
            CopilotApiResult.Error("Network error: ${e.message}", cause = e)
        } catch (e: Exception) {
            logger.error("Unexpected error getting models", e)
            CopilotApiResult.Error("Unexpected error: ${e.message}", cause = e)
        }
    }
    
    /**
     * Send a chat completion request
     */
    suspend fun chatCompletion(request: ChatCompletionRequest): CopilotApiResult<ChatCompletionResponse> = withContext(Dispatchers.IO) {
        try {
            val tokenResult = tokenManager.getValidCopilotToken()
            if (tokenResult !is AuthResult.Success<*>) {
                return@withContext CopilotApiResult.Error("Failed to get Copilot token: ${(tokenResult as AuthResult.Error).message}")
            }
            
            val requestBody = gson.toJson(request)
                .toRequestBody("application/json".toMediaType())
            
            val httpRequest = Request.Builder()
                .url(CHAT_COMPLETIONS_ENDPOINT)
                .post(requestBody)
                .header("Authorization", "Bearer ${tokenResult.data}")
                .header("Editor-Version", EDITOR_VERSION)
                .header("Content-Type", "application/json")
                .header("Copilot-Integration-Id", COPILOT_INTEGRATION_ID)
                .header("User-Agent", USER_AGENT)
                .build()
            
            val response = httpClient.newCall(httpRequest).execute()
            
            if (response.isSuccessful) {
                val responseBody = response.body?.string()
                if (responseBody != null) {
                    val completionResponse = gson.fromJson(responseBody, ChatCompletionResponse::class.java)
                    logger.info("Chat completion successful, ${completionResponse.choices.size} choices returned")
                    CopilotApiResult.Success(completionResponse)
                } else {
                    CopilotApiResult.Error("Empty response from chat completions endpoint")
                }
            } else {
                handleErrorResponse(response, "Chat completion failed")
            }
        } catch (e: IOException) {
            logger.error("Network error during chat completion", e)
            CopilotApiResult.Error("Network error: ${e.message}", cause = e)
        } catch (e: Exception) {
            logger.error("Unexpected error during chat completion", e)
            CopilotApiResult.Error("Unexpected error: ${e.message}", cause = e)
        }
    }
    
    /**
     * Send a text completion request
     */
    suspend fun textCompletion(request: TextCompletionRequest): CopilotApiResult<TextCompletionResponse> = withContext(Dispatchers.IO) {
        try {
            val tokenResult = tokenManager.getValidCopilotToken()
            if (tokenResult !is AuthResult.Success<*>) {
                return@withContext CopilotApiResult.Error("Failed to get Copilot token: ${(tokenResult as AuthResult.Error).message}")
            }
            
            val requestBody = gson.toJson(request)
                .toRequestBody("application/json".toMediaType())
            
            val httpRequest = Request.Builder()
                .url(TEXT_COMPLETIONS_ENDPOINT)
                .post(requestBody)
                .header("Authorization", "Bearer ${tokenResult.data}")
                .header("Editor-Version", EDITOR_VERSION)
                .header("Content-Type", "application/json")
                .header("Copilot-Integration-Id", COPILOT_INTEGRATION_ID)
                .header("User-Agent", USER_AGENT)
                .build()
            
            val response = httpClient.newCall(httpRequest).execute()
            
            if (response.isSuccessful) {
                val responseBody = response.body?.string()
                if (responseBody != null) {
                    val completionResponse = gson.fromJson(responseBody, TextCompletionResponse::class.java)
                    logger.info("Text completion successful, ${completionResponse.choices.size} choices returned")
                    CopilotApiResult.Success(completionResponse)
                } else {
                    CopilotApiResult.Error("Empty response from text completions endpoint")
                }
            } else {
                handleErrorResponse(response, "Text completion failed")
            }
        } catch (e: IOException) {
            logger.error("Network error during text completion", e)
            CopilotApiResult.Error("Network error: ${e.message}", cause = e)
        } catch (e: Exception) {
            logger.error("Unexpected error during text completion", e)
            CopilotApiResult.Error("Unexpected error: ${e.message}", cause = e)
        }
    }
    
    /**
     * Convenience method for simple chat completion
     */
    suspend fun simpleChat(
        message: String,
        systemPrompt: String? = null,
        temperature: Double = 0.1,
        maxTokens: Int? = null
    ): CopilotApiResult<String> {
        val request = CopilotRequestBuilder.chatCompletion(
            userMessage = message,
            systemMessage = systemPrompt,
            temperature = temperature,
            maxTokens = maxTokens
        )
        
        return when (val result = chatCompletion(request)) {
            is CopilotApiResult.Success -> {
                val firstChoice = result.data.choices.firstOrNull()
                val content = firstChoice?.message?.content
                if (content != null) {
                    CopilotApiResult.Success(content)
                } else {
                    CopilotApiResult.Error("No content in response")
                }
            }
            is CopilotApiResult.Error -> result
        }
    }
    
    /**
     * Convenience method for simple text completion
     */
    suspend fun simpleTextCompletion(
        prompt: String,
        maxTokens: Int? = null,
        temperature: Double = 0.1
    ): CopilotApiResult<String> {
        val request = CopilotRequestBuilder.textCompletion(
            prompt = prompt,
            maxTokens = maxTokens,
            temperature = temperature
        )
        
        return when (val result = textCompletion(request)) {
            is CopilotApiResult.Success -> {
                val firstChoice = result.data.choices.firstOrNull()
                val text = firstChoice?.text
                if (text != null) {
                    CopilotApiResult.Success(text)
                } else {
                    CopilotApiResult.Error("No text in response")
                }
            }
            is CopilotApiResult.Error -> result
        }
    }
    
    /**
     * Handle error responses from the API
     */
    private fun handleErrorResponse(response: Response, context: String): CopilotApiResult.Error {
        val errorBody = response.body?.string()
        
        return try {
            val apiError = gson.fromJson(errorBody, CopilotApiError::class.java)
            logger.error("$context: ${apiError.error.message}")
            CopilotApiResult.Error(
                message = apiError.error.message,
                code = apiError.error.code
            )
        } catch (e: Exception) {
            logger.error("$context: HTTP ${response.code} - $errorBody")
            when (response.code) {
                401 -> CopilotApiResult.Error("Authentication failed. Please re-authenticate.", "401")
                403 -> CopilotApiResult.Error("Access denied. Check your Copilot subscription.", "403")
                429 -> CopilotApiResult.Error("Rate limit exceeded. Please try again later.", "429")
                500 -> CopilotApiResult.Error("Copilot service error. Please try again later.", "500")
                else -> CopilotApiResult.Error("HTTP ${response.code}: ${response.message}", response.code.toString())
            }
        }
    }
}
