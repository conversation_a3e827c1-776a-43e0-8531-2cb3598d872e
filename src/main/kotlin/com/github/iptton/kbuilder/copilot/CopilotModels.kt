package com.github.iptton.kbuilder.copilot

/**
 * Data models for GitHub Copilot API
 */

/**
 * Chat message for completions
 */
data class ChatMessage(
    val role: String, // "user", "assistant", "system"
    val content: String
)

/**
 * Chat completion request
 */
data class ChatCompletionRequest(
    val messages: List<ChatMessage>,
    val intent: Boolean = false,
    val n: Int = 1,
    val temperature: Double = 0.1,
    val stream: Boolean = false,
    val model: String? = null,
    val max_tokens: Int? = null,
    val top_p: Double? = null,
    val frequency_penalty: Double? = null,
    val presence_penalty: Double? = null,
    val stop: List<String>? = null
)

/**
 * Choice in completion response
 */
data class CompletionChoice(
    val index: Int,
    val message: ChatMessage? = null,
    val text: String? = null,
    val finish_reason: String? = null,
    val logprobs: Any? = null
)

/**
 * Usage statistics
 */
data class CompletionUsage(
    val prompt_tokens: Int,
    val completion_tokens: Int,
    val total_tokens: Int
)

/**
 * Chat completion response
 */
data class ChatCompletionResponse(
    val id: String,
    val `object`: String,
    val created: Long,
    val model: String,
    val choices: List<CompletionChoice>,
    val usage: CompletionUsage? = null,
    val system_fingerprint: String? = null
)

/**
 * Available model information
 */
data class CopilotModel(
    val id: String,
    val `object`: String,
    val created: Long? = null,
    val owned_by: String? = null,
    val capabilities: ModelCapabilities? = null
)

/**
 * Model capabilities
 */
data class ModelCapabilities(
    val type: String? = null,
    val tokenizer: String? = null,
    val chat_completion: Boolean? = null,
    val text_completion: Boolean? = null,
    val embeddings: Boolean? = null
)

/**
 * Models list response
 */
data class ModelsResponse(
    val `object`: String,
    val data: List<CopilotModel>
)

/**
 * Text completion request (for legacy endpoints)
 */
data class TextCompletionRequest(
    val prompt: String,
    val max_tokens: Int? = null,
    val temperature: Double = 0.1,
    val top_p: Double? = null,
    val n: Int = 1,
    val stream: Boolean = false,
    val logprobs: Int? = null,
    val echo: Boolean = false,
    val stop: List<String>? = null,
    val presence_penalty: Double? = null,
    val frequency_penalty: Double? = null,
    val best_of: Int? = null,
    val logit_bias: Map<String, Double>? = null,
    val user: String? = null,
    val model: String? = null
)

/**
 * Text completion response
 */
data class TextCompletionResponse(
    val id: String,
    val `object`: String,
    val created: Long,
    val model: String,
    val choices: List<CompletionChoice>,
    val usage: CompletionUsage? = null
)

/**
 * Error response from Copilot API
 */
data class CopilotApiError(
    val error: CopilotErrorDetails
)

/**
 * Error details
 */
data class CopilotErrorDetails(
    val message: String,
    val type: String? = null,
    val param: String? = null,
    val code: String? = null
)

/**
 * Streaming response chunk
 */
data class StreamingChunk(
    val id: String,
    val `object`: String,
    val created: Long,
    val model: String,
    val choices: List<StreamingChoice>
)

/**
 * Streaming choice
 */
data class StreamingChoice(
    val index: Int,
    val delta: ChatMessage? = null,
    val finish_reason: String? = null
)

/**
 * API response wrapper
 */
sealed class CopilotApiResult<out T> {
    data class Success<T>(val data: T) : CopilotApiResult<T>()
    data class Error(val message: String, val code: String? = null, val cause: Throwable? = null) : CopilotApiResult<Nothing>()
}

/**
 * Convenience functions for creating common requests
 */
object CopilotRequestBuilder {
    
    /**
     * Create a simple chat completion request
     */
    fun chatCompletion(
        userMessage: String,
        systemMessage: String? = null,
        temperature: Double = 0.1,
        maxTokens: Int? = null
    ): ChatCompletionRequest {
        val messages = mutableListOf<ChatMessage>()
        
        systemMessage?.let {
            messages.add(ChatMessage("system", it))
        }
        
        messages.add(ChatMessage("user", userMessage))
        
        return ChatCompletionRequest(
            messages = messages,
            temperature = temperature,
            max_tokens = maxTokens
        )
    }
    
    /**
     * Create a text completion request
     */
    fun textCompletion(
        prompt: String,
        maxTokens: Int? = null,
        temperature: Double = 0.1
    ): TextCompletionRequest {
        return TextCompletionRequest(
            prompt = prompt,
            max_tokens = maxTokens,
            temperature = temperature
        )
    }
    
    /**
     * Create a conversation from message history
     */
    fun conversationCompletion(
        messageHistory: List<Pair<String, String>>, // List of (role, content) pairs
        newUserMessage: String,
        temperature: Double = 0.1
    ): ChatCompletionRequest {
        val messages = messageHistory.map { (role, content) ->
            ChatMessage(role, content)
        }.toMutableList()
        
        messages.add(ChatMessage("user", newUserMessage))
        
        return ChatCompletionRequest(
            messages = messages,
            temperature = temperature
        )
    }
}
